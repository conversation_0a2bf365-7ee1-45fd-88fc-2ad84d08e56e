import Gap from "@/features/shared/components/Gap";
import HeaderEventsFilter from "@/features/shared/components/HeaderEventsFilter";
import InputSelect from "@/features/shared/components/InputSelect";
import Layout from "@/features/shared/components/Layout";
import SectionHeading from "@/features/shared/components/SectionHeader";
import EventsList from "@/features/events/components/EventsList";
import FilterEventsSection from "@/features/events/components/FilterEventsSection";
import useGetEvents from "@/features/events/model";
import useEventFilterStore, {
  eventSortItems,
} from "@/features/events/useEventFilterStore";
import globalStyles from "@/lib/globalStyles";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useMemo } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { useTranslation } from "react-i18next";

export default function SearchScreen() {
  const { showAll } = useLocalSearchParams<{ showAll?: string }>();
  const { t } = useTranslation();

  useEffect(() => {
    if (showAll === "true") {
      clearFilter();
      setShowFilters(false);
    }
  }, [showAll]);

  const {
    search,
    sortBy,
    sortOrder,
    selectedOrganizer,
    selectedCategory,
    selectedLocation,
    filtersList,
    clearFilter,
    setSortBy,
    setSortOrder,
    selectedStartDate,
    setShowFilters,
    showFilters,
  } = useEventFilterStore();

  const categoryId = selectedCategory?._id;
  const title = selectedCategory?.name || t("search.title");

  const sortItems = useMemo(
    () =>
      eventSortItems.map((item) => ({
        name: t(`common.${item.key}`),
        value:
          item.sortBy && item.sortOrder
            ? `${item.sortBy}-${item.sortOrder}`
            : undefined,
      })),
    []
  );

  const { events, isLoading, isFetching, handleRefresh } = useGetEvents({
    categoryId: categoryId === "all" ? undefined : categoryId,
    searchKey: search || undefined,
    sortBy,
    sortOrder,
    organizer: selectedOrganizer,
    startAt: selectedStartDate?.toISOString(),
    location: selectedLocation,
  });

  return (
    <Layout
      title={{ text: title }}
      showSearch
      headerFilter={<HeaderEventsFilter />}
      noScroll
      onRefresh={handleRefresh}
    >
      {showFilters ? (
        <>
          <SectionHeading
            children={<> {t("search.find_events")}</>}
            style={{
              textAlign: "center",
            }}
          />
          <Gap y={25} />
          <FilterEventsSection
            events={events}
            onConclude={() => setShowFilters(false)}
            isLoading={isFetching || isLoading}
          />
        </>
      ) : (
        <View
          style={{
            gap: globalStyles.size.xs,
            borderBottomWidth: 2,
            borderBottomColor: globalStyles.colors.light.primary,
            paddingBottom: globalStyles.size.xl,
          }}
        >
          <TouchableOpacity onPress={() => clearFilter()}>
            <Text
              style={{
                textDecorationLine: "underline",
                color: globalStyles.colors.primary1,
              }}
            >
              ({filtersList().length}) {t("search.remove_filters")}
            </Text>
          </TouchableOpacity>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: globalStyles.colors.dark.primary,
              }}
            >
              {events.length} {t("search.results")}
            </Text>
            <InputSelect
              label={t("search.order_by")}
              items={sortItems}
              selected={sortItems.find(
                (v) =>
                  v.value === `${sortBy}-${sortOrder}` ||
                  (v.value === undefined &&
                    sortBy === undefined &&
                    sortOrder === undefined)
              )}
              onSelectItem={(item) => {
                const [value, order] = item.value?.split("-") || [];
                setSortBy((value as keyof typeof sortBy) || undefined);
                setSortOrder((order as keyof typeof sortOrder) || undefined);
              }}
              style={{
                flex: 0,
                paddingHorizontal: 10,
                paddingVertical: 4,
              }}
              dropdownStyle={{
                minWidth: "30%",
              }}
              listStyle={{
                width: "40%",
                left: "55%",
              }}
              textStyle={{
                fontSize: globalStyles.size.md,
              }}
              theme="primary"
            />
          </View>
        </View>
      )}
      <Gap y={globalStyles.gap.xs} />

      {!showFilters && (
        <EventsList
          noScroll={false}
          events={events}
          isFetching={isFetching}
          isLoading={isLoading}
          onRefresh={handleRefresh}
        />
      )}
    </Layout>
  );
}

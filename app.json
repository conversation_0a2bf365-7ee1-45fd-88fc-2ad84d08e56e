{"expo": {"name": "zimbora", "slug": "zimbora", "version": "1.6.0", "orientation": "portrait", "icon": "./assets/icon.png", "description": "Aplicativo de agenda cultural", "scheme": "zimbora", "userInterfaceStyle": "light", "newArchEnabled": true, "ios": {"config": {"usesNonExemptEncryption": false}, "supportsTablet": true, "bundleIdentifier": "ao.zimbora", "associatedDomains": ["applinks:zimbora.ao"], "runtimeVersion": {"policy": "appVersion"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "ao.zimbora", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "zimbora.ao", "pathPrefix": "/app/events"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/icons/logo_icon.png"}, "plugins": ["expo-localization", "expo-font", "expo-web-browser", "expo-router", ["expo-splash-screen", {"image": "./assets/icons/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store", ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "react-native", "organization": "workattack"}], ["expo-notifications", {"icon": "./assets/adaptive-icon.png", "defaultChannel": "default", "color": "#ffffff", "enableBackgroundRemoteNotifications": false}]], "experiments": {"typedRoutes": true, "buildCacheProvider": "eas"}, "extra": {"router": {"origin": false}, "eas": {"projectId": "5afc1aac-302f-4085-a233-8ce166a2378f"}}, "owner": "workattack", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/5afc1aac-302f-4085-a233-8ce166a2378f"}}}
import { Ionicons } from "@expo/vector-icons";
import DatePicker from "react-native-date-picker";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import React, { useState, useEffect } from "react";
import { Pressable, StyleSheet, Text, TextInput, View } from "react-native";
import globalStyles from "@/lib/globalStyles";
import { cn } from "@/lib/utils";

// Configure dayjs with custom parse format plugin
dayjs.extend(customParseFormat);

const formatDateSafely = (dateValue?: Date): string | null => {
  if (!dateValue) return null;
  try {
    const dayjsDate = dayjs(dateValue);
    return dayjsDate.isValid() ? dayjsDate.format("DD-MM-YYYY") : null;
  } catch (error) {
    return null;
  }
};

const parseDateFromText = (text: string): Date | null => {
  if (!text || text.length !== 10) return null;
  const parsed = dayjs(text, "DD-MM-YYYY", true);
  return parsed.isValid() ? parsed.toDate() : null;
};

export default function MyDatePicker({
  date,
  label,
  onChange,
  className,
  theme = "primary",
  maximumDate,
  minimumDate,
  defaultValue,
  required = false,
}: {
  date?: Date;
  label: string;
  onChange: (newDate: Date) => void;
  className?: string;
  theme?: "primary" | "secondary";
  maximumDate?: Date;
  minimumDate?: Date;
  defaultValue?: Date;
  required?: boolean;
}) {
  const [show, setShow] = useState(false);
  const [textValue, setTextValue] = useState(
    () => formatDateSafely(date) || formatDateSafely(defaultValue) || undefined
  );

  useEffect(() => {
    setTextValue(
      formatDateSafely(date) || formatDateSafely(defaultValue) || undefined
    );
  }, [date, defaultValue]);

  const handleDateConfirm = (selectedDate: Date) => {
    onChange(selectedDate);
    setShow(false);
  };

  const handleDateCancel = () => {
    setShow(false);
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);

    if (text.length !== 10) return;
    const parsedDate = parseDateFromText(text);
    if (!parsedDate) return;
    onChange(parsedDate);
  };

  const getThemeStyle = () => {
    switch (theme) {
      case "secondary":
        return styles.secondaryTheme;
      default:
        return styles.primaryTheme;
    }
  };

  const getTextStyle = () => {
    return theme === "primary" ? styles.primaryText : styles.secondaryText;
  };

  return (
    <View className={cn("relative", className)}>
      {required && (
        <View className="absolute top-0 left-0 size-5 rounded-full bg-primary-2 z-10" />
      )}
      {date && <Text style={styles.label}>{label}</Text>}
      <View style={[styles.inputContainer, getThemeStyle()]}>
        <TextInput
          style={[styles.textInput, getTextStyle()]}
          value={textValue}
          onChangeText={handleTextChange}
          placeholder="Ex: 01-01-2000"
          maxLength={10}
          keyboardType="numeric"
        />
        <Pressable style={styles.calendarButton} onPress={() => setShow(true)}>
          <Ionicons
            name="calendar-outline"
            className="text-xl text-primary-1"
          />
        </Pressable>
      </View>
      <DatePicker
        modal
        open={show}
        date={date || defaultValue || new Date()}
        mode="date"
        locale="pt-BR"
        onConfirm={handleDateConfirm}
        onCancel={handleDateCancel}
        maximumDate={maximumDate}
        minimumDate={minimumDate}
        theme="auto"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.rgba().tertiary2,
  },
  primaryTheme: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: globalStyles.rgba().primary1,
  },
  secondaryTheme: {
    backgroundColor: globalStyles.rgba().white,
  },
  text: {
    flex: 1,
    fontSize: globalStyles.size.lg,
    lineHeight: globalStyles.size.textAdjustLineHeight,
  },
  primaryText: {
    color: globalStyles.rgba().primary1,
  },
  secondaryText: {
    color: globalStyles.rgba().primary1,
  },
  smallSize: {
    paddingHorizontal: globalStyles.gap["2xs"],
    paddingVertical: 5,
  },
  mediumSize: {
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: 8,
  },
  smallTextSize: {
    fontSize: globalStyles.size.md,
  },
  mediumTextSize: {
    fontSize: globalStyles.size.lg,
  },
  inputContainer: {
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap.xs,
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
    width: "100%",
  },
  textInput: {
    flex: 1,
    fontSize: globalStyles.size.lg,
    color: globalStyles.rgba().primary1,
    lineHeight: 20,
    paddingVertical: globalStyles.gap["2xs"],
  },
  calendarButton: {
    justifyContent: "center",
    alignItems: "center",
  },
});
